<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="card card-default">
                <div class="card-body">
                    <?php echo Form::open(['route' => 'classroom.store','id'=>'createclassroom_form']); ?>

                    <?php echo $__env->make('Classroom::fields', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo Form::close(); ?>

                </div>
            </div>
        </div>
        <!-- /.card -->
    </div>
    </div>
</section>
<?php echo JsValidator::formRequest('Classroom\Http\Requests\CreateClassroomRequest', '#createclassroom_form'); ?>

<script>
     var createclassroomRoute = {
        store: "<?php echo e(route('classroom.store')); ?>",
    };
</script>
<script src="<?php echo e(asset(mix('js/page-level-js/timetable/classroom/create.js'))); ?>"></script><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Classroom/resources/views/create.blade.php ENDPATH**/ ?>