<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>Classrooms *</label>
            <select class="form-control select2" name="classroom">
                <?php $__currentLoopData = $classroom; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $class): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($class->id); ?>"><?php echo e($class->class_name); ?> (<?php echo e($class->department_name); ?>)</option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
    </div>
    <div class="col-md-12">
        <div class="form-group">
            <label>Days *</label>
            <select class="form-control select2" name="days[]" multiple>
                <option value="Monday">Monday</option>
                <option value="Tuesday">Tuesday</option>
                <option value="Wednesday">Wednesday</option>
                <option value="Thursday">Thursday</option>
                <option value="Friday">Friday</option>
                <option value="Saturday">Saturday</option>
            </select>
        </div>
    </div>
    <div class="col-md-12">
        <div class="form-group">
            <label>TimeSlots *</label>
            <select class="form-control select2" name="timeslots[]" multiple>
                <?php $__currentLoopData = $timeslots; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ts): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($ts->id); ?>"><?php echo e($ts->start_time); ?> - <?php echo e($ts->end_time); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
    </div>
    <?php echo Form::submit('Submit', ['id' => 'savect', 'class' => 'btn btn-primary']); ?>

    <button data-dismiss="modal" class="btn btn-secondary ml-2">Cancel </button>
</div>
<?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Timetable/resources/views/fields.blade.php ENDPATH**/ ?>