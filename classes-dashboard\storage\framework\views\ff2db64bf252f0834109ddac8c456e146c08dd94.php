<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <?php echo Form::label('subject_name', 'Subject Name *',['class' => 'form-label']); ?>

            <?php echo Form::text('subject_name', null, ['class' => 'form-control','placeholder' => 'Enter Subject Name']); ?>

        </div>
    </div>
    <div class="col-md-12">
        <div class="form-group">
            <label>Classroom *</label>
            <select class="form-control select2" name="classroom" id="classroom">
                <?php $__currentLoopData = $classroom; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $class): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option <?php echo e(isset($data) && $data->classroom_id == $class->id ? "selected" : ""); ?> value="<?php echo e($class->id); ?>"><?php echo e($class->class_name); ?> (<?php echo e($class->department_name); ?>)</option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
    </div>
    <?php echo Form::submit('Submit', ['id'=>'savesubject','class' => 'btn btn-primary']); ?>

    <button data-dismiss="modal" class="btn btn-secondary ml-2">Cancel </button>
</div><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Subject/resources/views/fields.blade.php ENDPATH**/ ?>