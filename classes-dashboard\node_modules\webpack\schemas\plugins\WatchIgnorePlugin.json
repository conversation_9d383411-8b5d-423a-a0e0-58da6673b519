{"title": "WatchIgnorePluginOptions", "type": "object", "additionalProperties": false, "properties": {"paths": {"description": "A list of RegExps or absolute paths to directories or files that should be ignored.", "type": "array", "items": {"description": "RegExp or absolute path to directories or files that should be ignored.", "anyOf": [{"instanceof": "RegExp", "tsType": "RegExp"}, {"type": "string"}]}, "minItems": 1}}, "required": ["paths"]}