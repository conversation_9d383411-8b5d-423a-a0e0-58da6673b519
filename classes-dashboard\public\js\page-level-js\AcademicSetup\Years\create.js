/******/ (() => { // webpackBootstrap
/*!****************************************************!*\
  !*** ./modules/Years/resources/views/js/create.js ***!
  \****************************************************/
$("#createyears_form").submit(function () {
  event.preventDefault();
  var form = $(this)[0];
  if ($(this).valid()) {
    ajaxHandler(form, yearsCreateRoute.store, 'post', '#createyears_form', '#saveyears', '#newYearsEntry', '#years_table');
    return false;
  }
});
/******/ })()
;