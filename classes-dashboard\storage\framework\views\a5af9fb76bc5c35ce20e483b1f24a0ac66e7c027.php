<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>Department *</label>
            <select class="form-control select2" name="department">
                <?php $__currentLoopData = $department; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dept): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option <?php echo e(isset($data) && $data->department_id == $dept->id ? "selected" : ""); ?> value="<?php echo e($dept->id); ?>"><?php echo e($dept->name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
    </div>
    <div class="col-md-12">
        <div class="form-group">
            <?php echo Form::label('class_name', 'Classroom Name *',['class' => 'form-label']); ?>

            <?php echo Form::text('class_name', null, ['class' => 'form-control','placeholder' => 'Enter Classroom Name']); ?>

        </div>
    </div>
    <?php if(isset($data)): ?>
    <div class=" col-sm-12 col-lg-12 mt-lg-12 mt-2 mt-2 ml-4 d-flex align-items-center ">
        <div class="form-group">
            <input name="department_update" value="true" id="department_update" type="checkbox" class="custom-control-input custom-control-input-danger custom-control-input-outline">
            <label for="department_update" class="custom-control-label">&nbsp;Transfer Student to new Department</label>
            <span style="color: black;" data-toggle="tooltip" data-placement="right" title="Only Select when want to transfer student" style="margin: 28px 0px;" aria-describedby="ui-id-2"><i class="fa fa-info-circle"></i></span>
        </div>
    </div>
    <?php endif; ?>
    <?php echo Form::submit('Submit', ['id'=>'saveclassroom','class' => 'btn btn-primary']); ?>

    <button data-dismiss="modal" class="btn btn-secondary ml-2">Cancel </button>
</div><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Classroom/resources/views/fields.blade.php ENDPATH**/ ?>