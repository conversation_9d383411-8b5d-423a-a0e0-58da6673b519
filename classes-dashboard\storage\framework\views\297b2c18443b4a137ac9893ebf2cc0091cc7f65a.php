
<?php $__env->startSection('content'); ?>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-12 main-title-flex">
                <h1>Resource</h1>
            </div>
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">

                <div class="card">
                    <!-- /.card-header -->
                    <div class="card-body">
                        <div class="generate-buttons">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create resource')): ?>
                            <a id="addResourceEntry" data-toggle="modal" data-target="#newResourceEntry" href="#" class="btn btn-primary"><i class="fa fa-plus-square"></i>&nbsp;Add New Resource</a>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('export resource data')): ?>
                            <button class="btn btn-dark exportData"><i class="fa fa-file-excel"></i>&nbsp; Export</button>
                            <?php endif; ?>
                        </div>
                        <table id="resource_table" class="table display  table-striped  table-borderless dt-responsive">
                            <thead>
                                <tr>
                                    <th>Action</th>
                                    <th>Name</th>
                                </tr>
                            </thead>
                            <tfoot>
                                <tr class="search-row">
                                    <th>Action</th>
                                    <th>Name</th>
                                </tr>
                            </tfoot>
                        </table>
                        <div class="modal" id="newResourceEntry" role="dialog" aria-labelledby="roleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h3 id="modeltitle" class="box-title popup-title m-0">Add New Entry</h3>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div id="createContent"></div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>

<!-- /.content -->
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
<script>
     var resourceRoute = {
        index: "<?php echo e(route('resource.index')); ?>",
        create: "<?php echo e(route('resource.create')); ?>",
        edit: "<?php echo e(route('resource.edit',':editdid')); ?>",
        delete: "<?php echo e(route('resource.destroy',':did')); ?>",
        export: "<?php echo e(route('resource.export')); ?>",
    };
</script>
<script src="<?php echo e(asset(mix('js/page-level-js/timetable/resource/index.js'))); ?>"></script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Resources/resources/views/index.blade.php ENDPATH**/ ?>