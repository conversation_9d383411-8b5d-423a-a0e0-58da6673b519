<?php if(count($ctdata) > 0): ?>
    <div class="table-responsive">
        <table id="ajaxtable" class="table table-bordered" style="text-align: center;">
            <thead>
                <tr>
                    <th>TimeSlot</th>
                    <?php for($d = $startdate->copy(); $d->lte($enddate); $d->addDay()): ?>
                        <th><?php echo e($d->format('l')); ?> <br /> (<?php echo e($d->format('d-m-Y')); ?>)</th>
                    <?php endfor; ?>
                </tr>
            </thead>
            <tbody>
                <?php $__currentLoopData = $timeslots; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $timeslot): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td>
                            <span
                                class="badge text-center  <?php echo e($timeslot->is_break == 'Yes' ? 'bg-danger' : 'bg-success'); ?>">
                                <?php echo e($timeslot->start_time); ?> - <?php echo e($timeslot->end_time); ?>

                            </span>
                        </td>
                        <?php for($d = $startdate->copy(); $d->lte($enddate); $d->addDay()): ?>
                            <td <?php if($date == $d->format('d-m-Y')): ?> class="active-day" <?php endif; ?>>
                                <!-- Main Slot -->
                                <?php $__currentLoopData = $ctdata->where('days', $d->format('l')); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $slot): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($slot->timeslot_id == $timeslot->id): ?>
                                        <?php if($slot->timeslot_is_break == 'Yes'): ?>
                                            <span>
                                                <?php echo e($slot->department_name); ?></span>
                                            <br />
                                            <span>
                                                <b><?php echo e($slot->class_name); ?></b></span>
                                            <br />
                                            <span><b>Break Name</b> :
                                                <?php echo e($slot->timeslot_break_name); ?>

                                            </span>
                                        <?php else: ?>
                                            <span>
                                                <br />
                                                <span><?php echo e($slot->resource_name); ?></span>
                                                <br />
                                                <span>
                                                    <?php echo e($slot->department_name); ?></span>
                                                <br />
                                                <span>
                                                    <b><?php echo e($slot->class_name); ?></b></span>
                                                <br />
                                                <span>
                                                    <b><i><?php echo e($slot->subject_name); ?></i></b></span>
                                                <br />
                                                <?php if(isset($slot->notes)): ?>
                                                    <br />
                                                    <span>
                                                        <b>Note:</b> (<?php echo e($slot->notes); ?>)
                                                    </span>
                                                <?php endif; ?>
                                                <br />
                                            </span>
                                            <hr />
                                        <?php endif; ?>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <!-- Main Slot -->
                            </td>
                        <?php endfor; ?>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    </div>    
<?php endif; ?><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Timetable/resources/views/ViewTimetable/ajaxtimetable.blade.php ENDPATH**/ ?>