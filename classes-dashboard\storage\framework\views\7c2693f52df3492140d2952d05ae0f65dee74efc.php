
<?php $__env->startSection('content'); ?>
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-12 main-title-flex">
                <h1>Annual Calendar</h1>
            </div>
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <!-- /.card-header -->
                    <div class="card-body">
                        <div id="CalendarContent">
                            <div class="row">
                                <div class="col-md-8"></div>
                                <div class="col-md-4">
                                    <div class="external-event bg-info">
                                        <input class="cs" value="4" type="checkbox" checked> Birthdays
                                    </div>
                                    <div class="external-event bg-success">
                                        <input class="cs" value="2" type="checkbox" checked> Holidays
                                    </div>
                                    <div class="external-event bg-warning">
                                        <input class="cs" value="3" type="checkbox" checked> Events
                                    </div>
                                </div>
                            </div>
                            <div id='calendar'></div>

                        </div>
                    </div>
                </div>
            </div>
</section>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
<script>
     var calenderRoute = {
        index: '<?php echo e(route("calendarjson")); ?>',
    };
</script>
<script src="<?php echo e(asset(mix('js/page-level-js/annualCalendar/index.js'))); ?>"></script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/AnnualCalendar/resources/views/index.blade.php ENDPATH**/ ?>