<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="card card-default">
                <div class="card-body">
                    <?php echo Form::open(['route' => 'subject.store','id'=>'createsubject_form']); ?>

                    <?php echo $__env->make('Subject::fields', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo Form::close(); ?>

                </div>
            </div>
        </div>
        <!-- /.card -->
    </div>
    </div>
</section>
<?php echo JsValidator::formRequest('Subject\Http\Requests\CreateSubjectRequest', '#createsubject_form'); ?>

<script>
     var createsubjectRoute = {
        store: "<?php echo e(route('subject.store')); ?>",
    };
</script>
<script src="<?php echo e(asset(mix('js/page-level-js/timetable/subject/create.js'))); ?>"></script><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Subject/resources/views/create.blade.php ENDPATH**/ ?>