/******/ (() => { // webpackBootstrap
/*!***************************************************!*\
  !*** ./modules/Years/resources/views/js/index.js ***!
  \***************************************************/
var columns = [{
  data: 'action',
  name: 'action',
  orderable: false
}, {
  data: 'Year Name',
  name: 'Year Name'
}, {
  data: 'Start Date',
  name: 'Start Date'
}, {
  data: 'End Date',
  name: 'End Date'
}, {
  data: 'Status',
  name: 'Status'
}];
var table = commonDatatable('#years_table', yearsRoute.index, columns);
function tablescroll() {
  $('html, body').animate({
    scrollTop: $("#years_table").offset().top
  }, 1000);
}
$("#filter").on('click', function (event) {
  event.preventDefault();
  tablescroll();
  table.draw();
});
$('#filterreset').click(function () {
  event.preventDefault();
  $('#start_date').val("");
  $('#end_date').val("");
  $('#status').val("").trigger('change');
  tablescroll();
  table.draw();
});
$(document).on('click', '.deleteYears', function () {
  var yearsID = $(this).attr('data-yearsID');
  var url = yearsRoute["delete"];
  url = url.replace(':yearsID', yearsID);
  var params = $.extend({}, doAjax_params_default);
  params['url'] = url;
  params['requestType'] = "DELETE";
  params['successCallbackFunction'] = function successCallbackFunction(result) {
    toastr.success(result.success);
    table.draw();
  };
  var calert = function calert() {
    commonAjax(params);
  };
  commonAlert(calert);
});
$(document).on("click", "#addYearsEntry", function () {
  var params = $.extend({}, doAjax_params_default);
  params["url"] = yearsRoute.create;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Add New Years");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".editYearsEntry", function () {
  var editdid = $(this).attr("data-editYearsid");
  var url = yearsRoute.edit;
  url = url.replace(":editdid", editdid);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Edit Years");
    $("#createContent").html(result);
    $('#department').trigger('change');
    setTimeout(function () {
      $('#classroom').val($('#classroom_val').val()).trigger('change');
    }, 1000);
  };
  commonAjax(params);
});
$(document).ready(function () {
  $("body").delegate("#start_date", "focusin", function () {
    $(this).datepicker({
      dateFormat: "yy-mm-dd",
      changeMonth: true,
      changeYear: true,
      onSelect: function onSelect(dateStr) {
        var startDate = new Date(dateStr);
        var minEndDate = new Date(startDate);
        minEndDate.setFullYear(minEndDate.getFullYear() + 1);
        $("#end_date").val("");
        $("#end_date").datepicker("option", {
          minDate: minEndDate
        });
      }
    });
  });
  $("body").delegate("#end_date", "focusin", function () {
    $(this).datepicker({
      dateFormat: "yy-mm-dd",
      changeMonth: true,
      changeYear: true,
      minDate: "+1y" // Prevent selection before 1 year
    });
  });
});
$(document).on("click", ".changeStatus", function () {
  var yearsID = $(this).attr("data-editYearsid");
  var url = yearsRoute.changeStatus;
  url = url.replace(":yearsID", yearsID);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "POST";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    toastr.success(result.success);
    table.draw();
  };
  var calert = function calert() {
    commonAjax(params);
  };
  commonAlert(calert);
});
/******/ })()
;