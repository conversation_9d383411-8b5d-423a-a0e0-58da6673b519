/******/ (() => { // webpackBootstrap
/*!**************************************************!*\
  !*** ./modules/Years/resources/views/js/edit.js ***!
  \**************************************************/
$("#edityears_form").submit(function () {
  event.preventDefault();
  var form = $(this)[0];
  if ($(this).valid()) {
    ajaxHandler(form, yearsEditRoute.update, 'PATCH', '#edityears_form', '#saveyears', '#newYearsEntry', '#years_table');
    return false;
  }
});
/******/ })()
;