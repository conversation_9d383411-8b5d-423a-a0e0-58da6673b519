
<?php $__env->startSection('content'); ?>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-12 main-title-flex">
                <h1>Classroom</h1>
            </div>
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">

                <div class="card">
                    <!-- /.card-header -->
                    <div class="card-body">
                        <div class="generate-buttons">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create classroom')): ?>
                            <a id="addClassroomEntry" data-toggle="modal" data-target="#newClassroomEntry" href="#" class="btn btn-primary"><i class="fa fa-plus-square"></i>&nbsp;Add New Classroom</a>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('export classroom data')): ?>
                            <button class="btn btn-dark exportData"><i class="fa fa-file-excel"></i>&nbsp; Export</button>
                            <?php endif; ?>
                        </div>
                        <table id="classroom_table" class="table display  table-striped  table-borderless dt-responsive">
                            <thead>
                                <tr>
                                    <th>Action</th>
                                    <th>Name</th>
                                    <th>Department</th>
                                </tr>
                            </thead>
                            <tfoot>
                                <tr class="search-row">
                                    <th>Action</th>
                                    <th>Name</th>
                                    <th>Department</th>
                                </tr>
                            </tfoot>
                        </table>
                        <div class="modal" id="newClassroomEntry" role="dialog" aria-labelledby="roleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h3 id="modeltitle" class="box-title popup-title m-0">Add New Entry</h3>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div id="createContent"></div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>

<!-- /.content -->
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
<script>
     var classroomRoute = {
        index: "<?php echo e(route('classroom.index')); ?>",
        create: "<?php echo e(route('classroom.create')); ?>",
        edit: "<?php echo e(route('classroom.edit',':editdid')); ?>",
        delete: "<?php echo e(route('classroom.destroy',':did')); ?>",
        export: "<?php echo e(route('classroom.export')); ?>",
    };
</script>
<script src="<?php echo e(asset(mix('js/page-level-js/timetable/classroom/index.js'))); ?>"></script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Classroom/resources/views/index.blade.php ENDPATH**/ ?>