
<?php $__env->startSection('content'); ?>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-12 main-title-flex">
                <h1>Subject</h1>
            </div>
        </div>
    </div>
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <!-- /.card-header -->
                    <div class="card-body">
                        <h3 class="box-title popup-title m-0">Filter Subject Data</h3>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label><b>Department</b></label>
                                    <select id="department" class="form-control select2">
                                        <option value="">Select department</option>
                                        <?php $__currentLoopData = $department; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($value->id); ?>"><?php echo e($value->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label><b>Classroom</b></label>
                                    <select id="classroom-filter" class="form-control select2 classroom-data">
                                        <option value="">Select Classroom</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <button id="filter" type="submit" class="btn btn-primary filter-btn">Filter</button>
                                    <button id="filterreset" class="btn btn-secondary filter-btn">Reset</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="generate-buttons">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create subject')): ?>
                            <a id="addSubjectEntry" data-toggle="modal" data-target="#newSubjectEntry" href="#" class="btn btn-primary"><i class="fa fa-plus-square"></i>&nbsp;Add New Subject</a>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('export subject data')): ?>
                            <button class="btn btn-dark exportData"><i class="fa fa-file-excel"></i>&nbsp; Export</button>
                            <?php endif; ?>
                        </div>
                        <table id="subject_table" class="table display  table-striped  table-borderless dt-responsive">
                            <thead>
                                <tr>
                                    <th>Action</th>
                                    <th>Name</th>
                                    <th>Classroom</th>
                                </tr>
                            </thead>
                            <tfoot>
                                <tr class="search-row">
                                    <th>Action</th>
                                    <th>Name</th>
                                    <th>Classroom</th>
                                </tr>
                            </tfoot>
                        </table>
                        <div class="modal" id="newSubjectEntry" role="dialog" aria-labelledby="roleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h3 id="modeltitle" class="box-title popup-title m-0">Add New Entry</h3>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div id="createContent"></div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
<script>
    var subjectRoute = {
        index: "<?php echo e(route('subject.index')); ?>",
        create: "<?php echo e(route('subject.create')); ?>",
        edit: "<?php echo e(route('subject.edit',':editdid')); ?>",
        delete: "<?php echo e(route('subject.destroy',':did')); ?>",
        export: "<?php echo e(route('subject.export')); ?>",
    };
</script>
<script src="<?php echo e(asset(mix('js/page-level-js/timetable/subject/index.js'))); ?>"></script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Subject/resources/views/index.blade.php ENDPATH**/ ?>