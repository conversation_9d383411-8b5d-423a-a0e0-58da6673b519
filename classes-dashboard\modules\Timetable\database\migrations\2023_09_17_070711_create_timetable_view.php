<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateTimetableView extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
   public function up()
{
    DB::statement('DROP VIEW IF EXISTS timetable_view;');

    DB::statement('
        CREATE VIEW timetable_view AS
        SELECT
            ct.id AS slot_id,
            ct.days AS days,
            ct.notes AS notes,
            c.id AS class_id,
            c.class_name AS class_name,
            t.id AS timeslot_id,
            t.start_time AS timeslot_start_time,
            t.end_time AS timeslot_end_time,
            t.break_name AS timeslot_break_name,
            t.is_break AS timeslot_is_break,
            r.resource_name AS resource_name,
            r.id AS resource_id,
            d.id AS department_id,
            d.name AS department_name,
            s.id AS subject_id,
            s.subject_name AS subject_name
        FROM
            master_timetable ct
        LEFT JOIN classrooms c ON c.id = ct.classroom_id
        LEFT JOIN timeslots t ON t.id = ct.timeslot_id
        LEFT JOIN resources r ON r.id = ct.resource_id
        LEFT JOIN subjects s ON s.id = ct.subject_id
        LEFT JOIN department d ON d.id = c.department_id;
    ');
}

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement('DROP VIEW IF EXISTS timetable_view;');
    }
}
