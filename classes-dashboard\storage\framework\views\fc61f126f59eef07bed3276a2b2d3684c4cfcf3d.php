<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="card card-default">
                <div class="card-body">
                    <?php echo Form::open(['route' => 'department.store','id'=>'createdepartment_form']); ?>

                    <?php echo $__env->make('Department::fields', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo Form::close(); ?>

                </div>
            </div>
        </div>
        <!-- /.card -->
    </div>
    </div>
</section>
<?php echo JsValidator::formRequest('Department\Http\Requests\CreateDepartmentRequest', '#createdepartment_form'); ?>

<script>
     var createDepartmentRoute = {
        store: "<?php echo e(route('department.store')); ?>",
    };
</script>
<script src="<?php echo e(asset(mix('js/page-level-js/department/create.js'))); ?>"></script>
<?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Department/resources/views/create.blade.php ENDPATH**/ ?>