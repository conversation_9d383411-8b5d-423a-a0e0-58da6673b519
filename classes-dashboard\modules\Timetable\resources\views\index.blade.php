@extends('layouts.app')
@section('content')

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-12 main-title-flex">
                <h1>Master Timetable</h1>
            </div>
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">

                <div class="card">
                    <!-- /.card-header -->
                    <div class="card-body">
                        <h3 class="box-title popup-title m-0">Filter Timetable</h3>
                        <form>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label><b>Department</b></label>
                                        <select class="form-control select2 department-filter" id="department_classroom" name="department" aria-invalid="false">
                                            <option value="">
                                                Select
                                            </option>
                                            @foreach($department as $dept)
                                            <option {{request()->query('department') == $dept->id ? "selected" : ""}} value="{{$dept->id}}">
                                                {{$dept->name}}
                                            </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label><b>Classroom</b></label>
                                        <select class="form-control select2 user-filter classroom-data" id="classroom_select" name="classroom" aria-invalid="false">
                                            <option value="">
                                                Select
                                            </option>
                                            @foreach($classroomforfilter as $class)
                                            <option {{request()->query('classroom') == $class->id ? "selected" : ""}} value="{{$class->id}}">
                                                {{$class->class_name}}
                                            </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="form-group">
                                        <button id="filtertimetable" class="btn btn-primary filter-btn">Filter</button>
                                        <a href="{{ route('master-timetable.index') }}" class="btn btn-secondary filter-btn">Reset</a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <div class="col-12">
                <div class="card">
                    <!-- /.card-header -->
                    <div class="card-body">
                        <div class="generate-buttons">
                            <a id="addCT" data-toggle="modal" data-target="#newCTEntry" href="#" class="btn btn-primary"><i class="fa fa-plus-square"></i>&nbsp;Assign Timeslots</a>
                        </div>
                        <div class="table-responsive">
                            @if (isset($ctdata) && !empty($ctdata))
                            <table id="ct_table" class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Monday</th>
                                        <th>Tuesday</th>
                                        <th>Wednesday</th>
                                        <th>Thursday</th>
                                        <th>Friday</th>
                                        <th>Saturday</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($classroom as $class)
                                    <tr>
                                        @for ($i = 0; $i < 6; $i++) <td>
                                            @foreach($ctdata->where('classroom_id',$class->id)->where('days',jddayofweek($i,1)) as $time)
                                            <span @if($time->timeslot->is_break == "no") class="addsub" data-toggle="modal" data-target="#newCTEntry" @endif data-classid="{{$class->id}}" data-slotid="{{$time->id}}">

                                                <span class="table-timeslots  ">
                                                    <span data-toggle="tooltip" title="Click here to assign subject." class="badge text-center  {{ $time->timeslot->is_break == "Yes" ? 'bg-danger' : 'bg-success' }}">{{$time->timeslot->start_time}} - {{$time->timeslot->end_time}}</span>
                                                    {!! Form::open(['route' => ['master-timetable.destroy', $time->id]]) !!}
                                                    @method('DELETE')
                                                    <button onclick="event.stopPropagation();" class="btn"><i class="fa fa-times" aria-hidden="true"></i></button>
                                                    {!! Form::close() !!}
                                                </span>
                                                @if(isset($time->resource))
                                                <span><b>Resource</b> : {{$time->resource->resource_name}}</span>
                                                <br />
                                                @endif
                                                @if(isset($time->subjects))
                                                <span><b>Subject</b> :
                                                    {{$time->subjects->subject_name}}</span>
                                                <br />
                                                @endif
                                                @if(isset($time->notes))
                                                <br />
                                                <span><b>Note</b> :
                                                    {{$time->notes}}
                                                </span>
                                                @endif
                                                @if($time->timeslot->is_break == "Yes")
                                                <span><b>Break Name</b> :
                                                    {{$time->timeslot->break_name}}
                                                </span> @endif
                                            </span>
                                            <hr />
                                            @endforeach
                                            </td>
                                            @endfor
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                            @endif
                        </div>
                        <div class="modal" id="newCTEntry" role="dialog" aria-labelledby="roleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h3 id="modeltitle" class="box-title popup-title m-0">Add New Entry</h3>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div id="createContent"></div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>

<!-- /.content -->
@endsection
@section('scripts')
@if(session()->has('success'))
<script>
    $(document).ready(function() {
        toastr.success('{{ session()->get("success") }}')
    });
</script>
@endif
<script>
     var masterTimetableRoute = {
        create: "{{ route('master-timetable.create') }}",
        subjectforclass: "{{ route('master-timetable.subject-timeslots') }}",
        getclassroomlist: "{{route('classroom.ajax.list')}}",
    };
</script>
<script src="{{ asset(mix('js/page-level-js/timetable/mastertimetable/index.js')) }}"></script>
@endsection