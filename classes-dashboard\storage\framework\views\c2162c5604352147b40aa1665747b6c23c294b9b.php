<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <?php echo Form::label('start_time', 'Start Time *',['class' => 'form-label']); ?>

            <?php echo Form::text('start_time', null, ['class' => 'form-control timepicker','placeholder' => 'Select Start Time']); ?>

        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <?php echo Form::label('end_time', 'End Time *',['class' => 'form-label']); ?>

            <?php echo Form::text('end_time', null, ['class' => 'form-control timepicker','placeholder' => 'Select End Time']); ?>

        </div>
    </div>
    <div class=" col-sm-12 col-lg-12 mt-lg-12 mt-2 mt-2 ml-4 d-flex align-items-center ">
        <div class="form-group">
            <input <?php echo e(isset($data) && $data->is_break == "Yes" ? "Checked" : ""); ?> name="is_break" value="Yes" id="is_break" type="checkbox" class="custom-control-input custom-control-input-danger custom-control-input-outline">
            <label for="is_break" class="custom-control-label">&nbsp;Is Break</label>
        </div>
    </div>
    <div class="col-md-12" id="break-div" style="display:none">
        <div class="form-group">
            <?php echo Form::label('break_name', 'Break Name *',['class' => 'form-label']); ?>

            <?php echo Form::text('break_name', null, ['class' => 'form-control','placeholder' => 'Enter Break Name']); ?>

        </div>
    </div>
    <?php echo Form::submit('Submit', ['id'=>'savetimeslots','class' => 'btn btn-primary']); ?>

    <button data-dismiss="modal" class="btn btn-secondary ml-2">Cancel </button>
</div>
<script type="text/javascript">
$(document).ready(function(){
    if($('#is_break').prop('checked')==true) {
        $('#break-div').show();
    }
    $('#is_break').change(function(){
        if(this.checked)
            $('#break-div').show();
        else
            $('#break-div').hide();
    });
});
</script><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Timeslots/resources/views/fields.blade.php ENDPATH**/ ?>