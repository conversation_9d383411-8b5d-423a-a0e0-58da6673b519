<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <?php echo Form::label('name', 'Department Name *', ['class' => 'form-label']); ?>

            <?php echo Form::text('name', null, [
                'class' => 'form-control ' . ($errors->has('name') ? 'is-invalid' : ''),
                'placeholder' => 'Enter Department Name',
            ]); ?>

        </div>
    </div>
    <div class=" col-sm-12 col-lg-12 mt-lg-12 mt-2 mt-2 ml-4 d-flex align-items-center ">
        <div class="form-group">
            <input name="educational" <?php if(isset($data->educational) && $data->educational == 1): ?> checked <?php endif; ?> value="true" id="educational"
                type="checkbox" class="custom-control-input custom-control-input-danger custom-control-input-outline">
            <label for="educational" class="custom-control-label">&nbsp; Educational</label>
        </div>
    </div>
    <?php echo Form::submit('Submit', ['id' => 'savedepartment', 'class' => 'btn btn-primary']); ?>

    <button data-dismiss="modal" class="btn btn-secondary ml-2">Cancel </button>
</div>
<?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Department/resources/views/fields.blade.php ENDPATH**/ ?>