
<?php $__env->startSection('content'); ?>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-12 main-title-flex">
                <h1>Master Timetable</h1>
            </div>
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <!-- /.card-header -->
                    <div class="card-body">
                        <h3 class="box-title popup-title m-0">Filter Timetable</h3>
                        <form>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label><b>Department</b></label>
                                        <select class="form-control select2 department-filter" id="department_classroom" name="department" aria-invalid="false">
                                            <option value="">
                                                Select
                                            </option>
                                            <?php $__currentLoopData = $department; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dept): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option <?php echo e(request()->query('department') == $dept->id ? "selected" : ""); ?> value="<?php echo e($dept->id); ?>">
                                                <?php echo e($dept->name); ?>

                                            </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label><b>Classroom</b></label>
                                        <select class="form-control select2 user-filter classroom-data" id="classroom_select" name="classroom" aria-invalid="false">
                                            <option value="">
                                                Select
                                            </option>
                                            <?php $__currentLoopData = $classroomforfilter; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $class): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option <?php echo e(request()->query('classroom') == $class->id ? "selected" : ""); ?> value="<?php echo e($class->id); ?>">
                                                <?php echo e($class->class_name); ?>

                                            </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="form-group">
                                        <button id="filtertimetable" class="btn btn-primary filter-btn">Filter</button>
                                        <a href="<?php echo e(route('master-timetable.index')); ?>" class="btn btn-secondary filter-btn">Reset</a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <div class="col-12">
                <div class="card">
                    <!-- /.card-header -->
                    <div class="card-body">
                        <div class="generate-buttons">
                            <a id="addCT" data-toggle="modal" data-target="#newCTEntry" href="#" class="btn btn-primary"><i class="fa fa-plus-square"></i>&nbsp;Assign Timeslots</a>
                        </div>
                        <div class="table-responsive">
                            <?php if(isset($ctdata) && !empty($ctdata)): ?>
                            <table id="ct_table" class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Monday</th>
                                        <th>Tuesday</th>
                                        <th>Wednesday</th>
                                        <th>Thursday</th>
                                        <th>Friday</th>
                                        <th>Saturday</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <?php for($i = 0; $i < 6; $i++): ?> <td>
                                            <?php $__currentLoopData = $ctdata->where('classroom_id',$class->id)->where('days',jddayofweek($i,1)); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $time): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <span <?php if($time->timeslot->is_break == "no"): ?> class="addsub" data-toggle="modal" data-target="#newCTEntry" <?php endif; ?> data-classid="<?php echo e($class->id); ?>" data-slotid="<?php echo e($time->id); ?>">

                                                <span class="table-timeslots  ">
                                                    <span data-toggle="tooltip" title="Click here to assign subject." class="badge text-center  <?php echo e($time->timeslot->is_break == "Yes" ? 'bg-danger' : 'bg-success'); ?>"><?php echo e($time->timeslot->start_time); ?> - <?php echo e($time->timeslot->end_time); ?></span>
                                                    <?php echo Form::open(['route' => ['master-timetable.destroy', $time->id]]); ?>

                                                    <?php echo method_field('DELETE'); ?>
                                                    <button onclick="event.stopPropagation();" class="btn"><i class="fa fa-times" aria-hidden="true"></i></button>
                                                    <?php echo Form::close(); ?>

                                                </span>
                                                <?php if(isset($time->resource)): ?>
                                                <span><b>Resource</b> : <?php echo e($time->resource->resource_name); ?></span>
                                                <br />
                                                <?php endif; ?>
                                                <?php if(isset($time->subjects)): ?>
                                                <span><b>Subject</b> :
                                                    <?php echo e($time->subjects->subject_name); ?></span>
                                                <br />
                                                <?php endif; ?>
                                                <?php if(isset($time->notes)): ?>
                                                <br />
                                                <span><b>Note</b> :
                                                    <?php echo e($time->notes); ?>

                                                </span>
                                                <?php endif; ?>
                                                <?php if($time->timeslot->is_break == "Yes"): ?>
                                                <span><b>Break Name</b> :
                                                    <?php echo e($time->timeslot->break_name); ?>

                                                </span> <?php endif; ?>
                                            </span>
                                            <hr />
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </td>
                                            <?php endfor; ?>
                                    </tr>
                                </tbody>
                            </table>
                            <?php endif; ?>
                        </div>
                        <div class="modal" id="newCTEntry" role="dialog" aria-labelledby="roleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h3 id="modeltitle" class="box-title popup-title m-0">Add New Entry</h3>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div id="createContent"></div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>

<!-- /.content -->
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
<?php if(session()->has('success')): ?>
<script>
    $(document).ready(function() {
        toastr.success('<?php echo e(session()->get("success")); ?>')
    });
</script>
<?php endif; ?>
<script>
    var masterTimetableRoute = {
        create: "<?php echo e(route('master-timetable.create')); ?>",
        subjectforclass: "<?php echo e(route('master-timetable.subject-timeslots')); ?>",
        getclassroomlist: "<?php echo e(route('classroom.ajax.list')); ?>",
    };
</script>
<script src="<?php echo e(asset(mix('js/page-level-js/timetable/mastertimetable/index.js'))); ?>"></script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Timetable/resources/views/index.blade.php ENDPATH**/ ?>