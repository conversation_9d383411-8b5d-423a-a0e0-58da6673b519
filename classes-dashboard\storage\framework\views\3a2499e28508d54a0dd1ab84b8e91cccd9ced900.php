<nav class="main-header navbar navbar-expand navbar-white navbar-light">
    <ul class="navbar-nav">
        <li class="nav-item">
            <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
        </li>
    </ul>

    <ul class="navbar-nav ml-auto">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('read student')): ?>
            <!-- New Button for Sidebar Icons Modal -->
            <li class="nav-item">
                <a class="nav-link" href="#" data-toggle="modal" data-target="#sidebarIconsModal">
                    <i class="fas fa-th"></i> <!-- Icon for the button (grid layout icon) -->
                </a>
            </li>
            <li class="nav-item">
                <a id="openStudentGlobalModal" data-toggle="modal" data-target="#studentGlobalModal" href="#"
                    class="nav-link">
                    <i class="nav-icon fas fa-search"></i>
                </a>
            </li>
        <?php endif; ?>
        <li class="nav-item user-menu">
            <select id="yearChange">
                <?php
                    $years = geAllYear();
                    $activeYear = getActiveYearId();
                ?>

                <?php $__currentLoopData = $years; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $year): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($activeYear == $year->id): ?>
                        <option selected value="<?php echo e($year->id); ?>"><?php echo e($year->year_name); ?></option>
                    <?php else: ?>
                        <option value="<?php echo e($year->id); ?>"><?php echo e($year->year_name); ?></option>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </li>
        <li class="nav-item">
            <a href="" class="nav-link">
            </a>
        </li>
        <li>
            <input type="checkbox" class="sr-only" id="darkmode-toggle">
            <label for="darkmode-toggle" class="toggle">
                <span>Toggle dark mode</span>
            </label>

        </li>
        <li class="nav-item dropdown user-menu">
            <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
                <img src="<?php echo e($tenantLogo); ?>" class="user-image img-circle elevation-2">
                <span class="d-none d-md-inline"><?php echo e(Auth::user()->firstName); ?> <?php echo e(Auth::user()->lastName); ?></span>
            </a>
            <ul class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
                <!-- User image -->
                <li class="user-header bg-primary">
                    <img src="<?php echo e($tenantLogo); ?>" class="img-circle elevation-2">
                    <p>
                        <?php echo e(Auth::user()->firstName); ?> <?php echo e(Auth::user()->lastName); ?>

                        <small>Member since <?php echo date('M. Y', strtotime(Auth::user()->created_at)); ?></small>
                    </p>
                </li>
                <!-- Menu Footer-->
                <li class="user-footer">
                    <?php if(!Auth::user()->getIsSuperAdmin()): ?>
                        <a href="<?php echo e(route('users.show', Auth::id())); ?>" class="btn btn-primary">Profile</a>
                    <?php endif; ?>
                    <a href="<?php echo url('/logout'); ?>" class="btn btn-primary float-right"
                        onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                        Sign out
                    </a>
                    <form id="logout-form" action="<?php echo e(url('/logout')); ?>" method="POST" style="display: none;">
                        <?php echo e(csrf_field()); ?>

                    </form>
                </li>
            </ul>
        </li>
    </ul>
</nav>
<aside class="main-sidebar sidebar-dark-primary elevation-4">
    <a href="#" class="brand-link">

        <span class="brand-text font-weight-light"><?php echo e($tenantName); ?></span>    </a>
    <div class="sidebar">
        <div class="user-panel">
            <div class="pull-left image">
                <img src="<?php echo e($tenantLogo); ?>" class="user-image img-circle">
            </div>
            <div class="pull-left info">
                <p><?php echo e(Auth::user()->firstName); ?> <?php echo e(Auth::user()->lastName); ?></p>
            </div>
        </div>
        <nav class="mt-2">
            <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" 
            data-accordion="false">
                <li class="nav-item">
                    <a href="<?php echo e(route('home')); ?>" class="nav-link <?php echo e(request()->is('/') ? 'active' : ''); ?>">
                        <i class="nav-icon fas fa-tachometer-alt"></i>
                        <p>
                            Dashboard
                        </p>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo e(route('annualcalendarview')); ?>"
                        class="nav-link <?php echo e(request()->is('annual-calendar') ? 'active' : ''); ?>">
                        <i class="fa nav-icon  fas fa-calendar-week"></i>
                        <p>
                            Annual Calendar
                        </p>
                    </a>
                </li>

                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="nav-icon fa fa-cog"></i>
                        <p>
                            Academic Setup 
                            <i class="right fas fa-angle-left"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('read year')): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('years.index')); ?>"
                                    class="nav-link <?php echo e(request()->is('years') ? 'active' : ''); ?>">
                                    <i class="nav-icon fas fa-calendar"></i>
                                    <p>
                                        Year
                                    </p>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('read department')): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('department.index')); ?>"
                                    class="nav-link <?php echo e(request()->is('department') ? 'active' : ''); ?>">
                                    <i class="nav-icon fa fa-sitemap"></i>
                                    <p>
                                        Department
                                    </p>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('read classroom')): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('classroom.index')); ?>"
                                    class="nav-link <?php echo e(request()->is('classroom') ? 'active' : ''); ?>">
                                    <i class="nav-icon fas fa-restroom"></i>
                                    <p>Classroom</p>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('read subject')): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('subject.index')); ?>"
                                    class="nav-link <?php echo e(request()->is('subject') ? 'active' : ''); ?>">
                                    <i class="nav-icon fas fa-book"></i>
                                    <p>Subjects</p>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('read resource')): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('resource.index')); ?>"
                                    class="nav-link <?php echo e(request()->is('resource') ? 'active' : ''); ?>">
                                    <i class="nav-icon fas fa-school"></i>
                                    <p>Resources</p>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('read timeslot')): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('timeslots.index')); ?>"
                                    class="nav-link <?php echo e(request()->is('timeslots') ? 'active' : ''); ?>">
                                    <i class="nav-icon fas fa-clock"></i>
                                    <p>Timeslots</p>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('read event')): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('event.index')); ?>"
                                    class="nav-link <?php echo e(request()->is('event*') ? 'active' : ''); ?>">
                                    <i class="nav-icon fas fa-candy-cane"></i>
                                    <p>
                                        Events
                                    </p>
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('read holiday')): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('holiday.index')); ?>"
                                    class="nav-link <?php echo e(request()->is('holiday*') ? 'active' : ''); ?>">
                                    <i class="nav-icon fa fa-h-square"></i>
                                    <p>
                                        Holiday
                                    </p>
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage timetable')): ?>
                            <li class="nav-item">
                                <a href="<?php echo e(route('master-timetable.index')); ?>"
                                    class="nav-link <?php echo e(request()->is('master-timetable*') ? 'active' : ''); ?>">
                                    <i class="nav-icon fas fa-calendar-plus"></i>
                                    <p>
                                        Create Timetable
                                    </p>
                                </a>
                            </li>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-icon fa fa-cog"></i>
                                <p>
                                    Fee Setup 
                                    <i class="right fas fa-angle-left"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('read fees category')): ?>
                                    <li class="nav-item">
                                        <a href="<?php echo e(route('feesmanager.category.index')); ?>"
                                            class="nav-link <?php echo e(request()->is('feesmanager/category*') ? 'active' : ''); ?>">
                                            <i class="nav-icon fa fa-sitemap"></i>
                                            <p>
                                                Fees Category
                                            </p>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage classroom fees')): ?>
                                    <li class="nav-item">
                                        <a href="<?php echo e(route('feesmanager.classroom-wise-fee.index')); ?>"
                                            class="nav-link <?php echo e(request()->is('feesmanager/classroom-wise-fee**') ? 'active' : ''); ?>">
                                            <i class="nav-icon fas fa-restroom"></i>
                                            <p>
                                                Classroom Fees
                                            </p>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage classroom fees')): ?>
                                    <li class="nav-item">
                                        <a href="<?php echo e(route('enquiry.fee.setup.view')); ?>"
                                            class="nav-link <?php echo e(request()->is('enquiry-fee-setup*') ? 'active' : ''); ?>">
                                            <i class="nav-icon fas fa-restroom"></i>
                                            <p>
                                                Enquiry Fees
                                            </p>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a href="<?php echo e(route('timetable.view')); ?>"
                        class="nav-link <?php echo e(request()->is('timetable*') ? 'active' : ''); ?>">
                        <i class="nav-icon fas fa-calendar-check"></i>
                        <p>
                            Timetable Overview
                        </p>
                    </a>
                </li>
                <h3 class="main-title">Student</h3>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('read enquiry')): ?>
                    <li class="nav-item">
                        <a href="<?php echo e(route('enquiry.index')); ?>"
                            class="nav-link <?php echo e(request()->is('enquiry') ? 'active' : ''); ?>">
                            <i class="nav-icon fas fa-question-circle"></i>
                            <p>
                                Enquiry Management
                            </p>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('read student')): ?>
                    <li class="nav-item">
                        <a href="<?php echo e(route('student.index')); ?>"
                            class="nav-link <?php echo e(request()->is('student') ? 'active' : ''); ?>">
                            <i class="nav-icon fa fa-university"></i>
                            <p>
                                Student Management
                            </p>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('read document')): ?>
                    <li class="nav-item">
                        <a href="<?php echo e(route('documents.index')); ?>"
                            class="nav-link <?php echo e(request()->is('documents*') ? 'active' : ''); ?>">
                            <i class="nav-icon fas fa-file-signature unique-doc-icon"></i>
                            <p>
                                Documents
                            </p>
                        </a>
                    </li>
                <?php endif; ?>
                <?php
                    $classrooms = isClassTeacher();
                ?>
                <?php if($classrooms->count() > 0): ?>
                    <li class="nav-item">
                        <a href="<?php echo e(route('student-attendance')); ?>"
                            class="nav-link <?php echo e(request()->is('student-attendance') ? 'active' : ''); ?>">
                            <i class="nav-icon fas fa-chalkboard"></i>
                            <p>
                                Student Attendance
                            </p>
                        </a>
                    </li>
                <?php endif; ?>

                <h3 class="main-title">Finance</h3>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['manage student fees', 'manage enquiry fees', 'manage passbook'])): ?>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="nav-icon fa fa-file-invoice-dollar"></i>
                            <p>
                                Fees Collection
                                 <i class="right fas fa-angle-left"></i>
                                </p>
                        </a>
                        <ul class="nav nav-treeview">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage student fees')): ?>
                                <li class="nav-item">
                                    <a href="<?php echo e(route('getAllStudentPaymentLogs')); ?>"
                                        class="nav-link <?php echo e(request()->is('student-payment-data') ? 'active' : ''); ?>">
                                        <i class="nav-icon fa fa-rupee-sign"></i>
                                        <p>
                                            Fees Payments
                                        </p>
                                    </a>
                                </li>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage enquiry fees')): ?>
                                <li class="nav-item">
                                    <a href="<?php echo e(route('enquiry.payments')); ?>"
                                        class="nav-link <?php echo e(request()->is('enquiry-payments') ? 'active' : ''); ?>">
                                        <i class="nav-icon fa fa-question-circle"></i>
                                        <p>
                                            Enquiry Payments
                                        </p>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </li>
                <?php endif; ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage passbook')): ?>
                    <li class="nav-item">
                        <a href="<?php echo e(route('passbook')); ?>"
                            class="nav-link <?php echo e(request()->is('passbook') ? 'active' : ''); ?>">
                            <i class="nav-icon fa fa-file-invoice"></i>
                            <p>
                                Passbook
                            </p>
                        </a>
                    </li>
                <?php endif; ?>
                <h3 class="main-title">Others</h3>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['read circular'])): ?>
                    <li class="nav-item">
                        <a href="<?php echo e(route('circulars.index')); ?>"
                            class="nav-link <?php echo e(request()->is('circulars*') ? 'active' : ''); ?>">
                            <i class="nav-icon fas fa-newspaper"></i>
                            <p>
                                Circulars
                            </p>
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
</aside>
<?php echo $__env->make('layouts.searchModules', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\resources\views/layouts/sidebar.blade.php ENDPATH**/ ?>