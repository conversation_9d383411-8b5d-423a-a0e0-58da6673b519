<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="card card-default">
                <div class="card-body">
                    <?php echo Form::open(['route' => ['master-timetable.assign-subject',$sloddetails->id],'id'=>'createasubject_form']); ?>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Subjects *</label>
                                <select class="form-control select2" name="subject">
                                    <?php $__currentLoopData = $subjects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sb): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option 
                                    <?php echo e($sb->id == $sloddetails->subject_id ? 'selected' : ''); ?>

                                    value="<?php echo e($sb->id); ?>"><?php echo e($sb->subject_name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Resource *</label>
                                <select class="form-control select2" name="resource">
                                    <?php $__currentLoopData = $resource; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $rc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option 
                                    <?php echo e($rc->id == $sloddetails->resource_id ? 'selected' : ''); ?>

                                    value="<?php echo e($rc->id); ?>"><?php echo e($rc->resource_name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <?php echo Form::label('note', 'Note',['class' => 'form-label']); ?>

                                <?php echo Form::text('note', $sloddetails->notes, ['class' => 'form-control','placeholder' => 'Enter Note']); ?>

                            </div>
                        </div>
                        <?php echo Form::submit('Submit', ['id'=>'savect','class' => 'btn btn-primary']); ?>

                        <button data-dismiss="modal" class="btn btn-secondary ml-2">Cancel </button>
                    </div> <?php echo Form::close(); ?>

                </div>
            </div>
        </div>
        <!-- /.card -->
    </div>
    </div>
</section>
<?php echo JsValidator::formRequest('Timetable\Http\Requests\CreateClassroomWithSubjectRequest', '#createasubject_form'); ?><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Timetable/resources/views/subjectcreate.blade.php ENDPATH**/ ?>