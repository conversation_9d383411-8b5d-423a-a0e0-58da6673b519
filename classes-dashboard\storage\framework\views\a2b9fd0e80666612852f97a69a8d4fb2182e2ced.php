
<?php $__env->startSection('content'); ?>
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-9">
                <h1>Years</h1>
            </div>
        </div>
    </div>
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="generate-buttons">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create year')): ?>
                                <a id="addYearsEntry" data-toggle="modal"
                                data-target="#newYearsEntry" class="btn btn-primary">
                                    <i class="fa fa-plus-square"></i> Add New Years
                                </a>
                            <?php endif; ?>
                        </div>
                        <table id="years_table" class="table display  table-striped  table-borderless dt-responsive">
                            <thead>
                                <tr>
                                    <th>Action</th>
                                    <th>Year Name</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tfoot>
                                <tr class="search-row">
                                    <th>Action</th>
                                    <th>Year Name</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Status</th>
                                </tr>
                            </tfoot>
                        </table>
                        <div class="modal" id="newYearsEntry" role="dialog"
                        aria-labelledby="roleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h3 id="modeltitle" class="box-title popup-title m-0">Add New Entry</h3>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div id="createContent"></div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
<script>
     var yearsRoute = {
        index: "<?php echo e(route('years.index')); ?>",
        create: "<?php echo e(route('years.create')); ?>",
        edit: "<?php echo e(route('years.edit',':editdid')); ?>",
        delete: "<?php echo e(route('years.destroy',':yearsID')); ?>",
        changeStatus: "<?php echo e(route('years.active',':yearsID')); ?>",
    };
</script>
<script src="<?php echo e(asset(mix('js/page-level-js/AcademicSetup/Years/index.js'))); ?>"></script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Years/resources/views/index.blade.php ENDPATH**/ ?>